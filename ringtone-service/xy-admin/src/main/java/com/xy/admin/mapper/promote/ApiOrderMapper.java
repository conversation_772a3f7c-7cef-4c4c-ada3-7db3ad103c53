package com.xy.admin.mapper.promote;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xy.admin.entity.ApiOrder;
import com.xy.admin.vo.apiOrder.ApiOrderGroupQueryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 营销推广订单综合表(api_order)表数据库操作Mapper
 *
 * <AUTHOR>
 * @since 2025/7/30 15:39
 */
@Mapper
public interface ApiOrderMapper extends BaseMapper<ApiOrder> {

    /**
     * 动态分组查询
     * @param queryVO 查询条件
     * @return 分组统计结果
     */
    List<Map<String, Object>> selectGroupData(@Param("query") ApiOrderGroupQueryVO queryVO);

}