package com.xy.admin.vo.apiOrder;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * API订单分组查询VO
 * <AUTHOR>
 * @since 2025/7/30
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApiOrderGroupQueryVO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 分组字段列表（必填）
     * 支持的字段：app_id, channel_no, product_no, order_status, out_order_status, 
     * operation_type, platform, server, DATE(create_time), DATE(update_time)
     */
    @NotEmpty(message = "分组字段不能为空")
    @Size(max = 5, message = "最多支持5个分组字段")
    private List<String> groupFields;
    
    /**
     * 统计字段列表（可选，默认统计总数）
     * 支持的统计类型：COUNT, COUNT_DISTINCT_mobile_no, COUNT_DISTINCT_order_id
     */
    private List<String> aggregateFields;
    
    /**
     * 应用ID过滤
     */
    private String appId;
    
    /**
     * 渠道编号过滤
     */
    private String channelNo;
    
    /**
     * 产品编号过滤
     */
    private String productNo;
    
    /**
     * 手机号码过滤
     */
    private String mobileNo;
    
    /**
     * 订单状态码过滤
     */
    private String orderStatus;
    
    /**
     * 外部订单状态过滤
     */
    private String outOrderStatus;
    
    /**
     * 操作类型过滤：SMS-短信发送，ORDER-订单提交
     */
    private String operationType;
    
    /**
     * 平台名称过滤
     */
    private String platform;
    
    /**
     * 服务器过滤
     */
    private String server;
    
    /**
     * 创建时间范围查询 - 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTimeStart;
    
    /**
     * 创建时间范围查询 - 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTimeEnd;
    
    /**
     * 更新时间范围查询 - 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTimeStart;
    
    /**
     * 更新时间范围查询 - 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTimeEnd;
    
    /**
     * 排序字段（可选，默认按第一个分组字段排序）
     */
    private String orderBy;
    
    /**
     * 排序方向（ASC/DESC，默认DESC）
     */
    private String orderDirection = "DESC";
    
    /**
     * 限制返回结果数量（可选，默认1000）
     */
    private Integer limit = 1000;
}
