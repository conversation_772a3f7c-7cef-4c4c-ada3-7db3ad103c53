package com.xy.admin.vo.apiOrder;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * API订单分组查询结果VO
 * <AUTHOR>
 * @since 2025/7/30
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApiOrderGroupResultVO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 分组字段值映射
     * key: 字段名（如：app_id, channel_no等）
     * value: 字段值
     */
    private Map<String, Object> groupValues;
    
    /**
     * 统计结果映射
     * key: 统计字段名（如：total_count, unique_mobile_count等）
     * value: 统计值
     */
    private Map<String, Object> aggregateValues;
    
    /**
     * 总记录数
     */
    private Long totalCount;
    
    /**
     * 唯一手机号数量
     */
    private Long uniqueMobileCount;
    
    /**
     * 唯一订单ID数量
     */
    private Long uniqueOrderCount;
}
