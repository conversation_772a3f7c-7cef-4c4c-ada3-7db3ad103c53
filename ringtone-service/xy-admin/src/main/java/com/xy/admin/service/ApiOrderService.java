package com.xy.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xy.admin.entity.ApiOrder;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xy.admin.vo.apiOrder.ApiOrderQueryVO;
import com.xy.admin.vo.apiOrder.ApiOrderVO;

/**
 * 营销推广订单综合表(api_order)表服务接口
 *
 * <AUTHOR>
 * @since 2025/7/30 15:39
 */
public interface ApiOrderService extends IService<ApiOrder>{

    /**
     * 多字段分页查询
     * @param queryVO 查询条件
     * @return 分页结果
     */
    IPage<ApiOrderVO> queryPage(ApiOrderQueryVO queryVO);

}
