package com.xy.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xy.admin.annotation.DataSource;
import com.xy.admin.service.ApiOrderService;
import com.xy.admin.vo.apiOrder.ApiOrderQueryVO;
import com.xy.admin.vo.apiOrder.ApiOrderVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
* 营销推广订单综合表(api_order)表控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/api_order")
public class ApiOrderController {

    @Autowired
    private ApiOrderService apiOrderService;

    /**
     * 列表查询 - 支持多字段查询、分页和排序
     */
    @PostMapping("/list")
    @DataSource("secondary")
    public IPage<ApiOrderVO> list(@Valid @RequestBody ApiOrderQueryVO queryVO) {
        return apiOrderService.queryPage(queryVO);
    }
}
