package com.xy.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xy.admin.annotation.DataSource;
import com.xy.admin.service.ApiOrderService;
import com.xy.admin.vo.apiOrder.ApiOrderQueryVO;
import com.xy.admin.vo.apiOrder.ApiOrderVO;
import com.xy.admin.vo.apiOrder.ApiOrderGroupQueryVO;
import com.xy.admin.vo.apiOrder.ApiOrderGroupResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
* 营销推广订单综合表(api_order)表控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/api_order")
public class ApiOrderController {

    @Autowired
    private ApiOrderService apiOrderService;

    /**
     * 列表查询 - 支持多字段查询、分页和排序
     */
    @PostMapping("/list")
    @DataSource("secondary")
    public IPage<ApiOrderVO> list(@Valid @RequestBody ApiOrderQueryVO queryVO) {
        return apiOrderService.queryPage(queryVO);
    }

    /**
     * 分组统计查询 - 支持动态字段分组和多维度统计
     * /
     * 支持的分组字段：
     * - app_id: 应用ID
     * - channel_no: 渠道编号
     * - product_no: 产品编号
     * - order_status: 订单状态码
     * - out_order_status: 外部订单状态
     * - operation_type: 操作类型
     * - platform: 平台名称
     * - server: 服务器
     * - DATE(create_time): 创建日期
     * - DATE(update_time): 更新日期
     * /
     * 支持的统计类型：
     * - COUNT: 总记录数
     * - COUNT_DISTINCT_mobile_no: 唯一手机号数量
     * - COUNT_DISTINCT_order_id: 唯一订单ID数量
     */
    @PostMapping("/group")
    @DataSource("secondary")
    public List<ApiOrderGroupResultVO> groupQuery(@Valid @RequestBody ApiOrderGroupQueryVO queryVO) {
        return apiOrderService.groupQuery(queryVO);
    }
}
