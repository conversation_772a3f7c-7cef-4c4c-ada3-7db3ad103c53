package com.xy.admin.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xy.admin.entity.LogApi;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xy.admin.entity.ApiOrder;
import com.xy.admin.mapper.promote.ApiOrderMapper;
import com.xy.admin.service.ApiOrderService;
import com.xy.admin.vo.apiOrder.ApiOrderQueryVO;
import com.xy.admin.vo.apiOrder.ApiOrderVO;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 营销推广订单综合表(api_order)表服务实现
 *
 * <AUTHOR>
 * @since 2025/7/30 15:39
 */
@Service
public class ApiOrderServiceImpl extends ServiceImpl<ApiOrderMapper, ApiOrder> implements ApiOrderService{
    
    @Override
    public IPage<ApiOrderVO> queryPage(ApiOrderQueryVO queryVO) {
        // 使用流式编程构建查询条件
        LambdaQueryWrapper<ApiOrder> wrapper = Stream.of(queryVO)
                .map(this::buildQueryWrapper)
                .findFirst()
                .orElse(new LambdaQueryWrapper<>());
        
        // 分页查询
        Page<ApiOrder> page = queryVO.buildPage();
        IPage<ApiOrder> pageResult = this.page(page, wrapper);
        
        // 使用流转换为VO并构建返回结果
        return Optional.of(pageResult)
                .map(this::convertToVOPage)
                .orElse(new Page<>());
    }
    
    /**
     * 使用流式编程构建查询条件
     */
    private LambdaQueryWrapper<ApiOrder> buildQueryWrapper(ApiOrderQueryVO queryVO) {
        LambdaQueryWrapper<ApiOrder> wrapper = new LambdaQueryWrapper<>();
        
        // 使用流式编程处理字符串条件
        Stream.of(
                // 模糊查询条件
                buildCondition(queryVO.getChannelNo(), val -> wrapper.like(ApiOrder::getChannelNo, val)),
                buildCondition(queryVO.getProductNo(), val -> wrapper.like(ApiOrder::getProductNo, val)),
                buildCondition(queryVO.getAppPackage(), val -> wrapper.like(ApiOrder::getAppPackage, val)),
                buildCondition(queryVO.getAppName(), val -> wrapper.like(ApiOrder::getAppName, val)),
                buildCondition(queryVO.getPlatform(), val -> wrapper.like(ApiOrder::getPlatform, val)),
                buildCondition(queryVO.getRemark(), val -> wrapper.like(ApiOrder::getRemark, val)),
                
                // 精确查询条件
                buildCondition(queryVO.getMobileNo(), val -> wrapper.eq(ApiOrder::getMobileNo, val)),
                buildCondition(queryVO.getOrderId(), val -> wrapper.eq(ApiOrder::getOrderId, val)),
                buildCondition(queryVO.getOrderStatus(), val -> wrapper.eq(ApiOrder::getOrderStatus, val)),
                buildCondition(queryVO.getOutOrderStatus(), val -> wrapper.eq(ApiOrder::getOutOrderStatus, val)),
                buildCondition(queryVO.getOutOrderId(), val -> wrapper.eq(ApiOrder::getOutOrderId, val)),
                buildCondition(queryVO.getOperationType(), val -> wrapper.eq(ApiOrder::getOperationType, val)),
                buildCondition(queryVO.getServer(), val -> wrapper.eq(ApiOrder::getServer, val))
        ).forEach(Runnable::run);
        
        // 使用流处理时间范围条件
        Stream.of(
                buildTimeCondition(queryVO.getCreateTimeStart(), time ->
                        wrapper.ge(ApiOrder::getCreateTime, convertToDate(time))),
                buildTimeCondition(queryVO.getCreateTimeEnd(), time ->
                        wrapper.le(ApiOrder::getCreateTime, convertToDate(time))),
                buildTimeCondition(queryVO.getUpdateTimeStart(), time ->
                        wrapper.ge(ApiOrder::getUpdateTime, convertToDate(time))),
                buildTimeCondition(queryVO.getUpdateTimeEnd(), time ->
                        wrapper.le(ApiOrder::getUpdateTime, convertToDate(time)))
        ).forEach(Runnable::run);
        
        // 通用关键字查询
        Optional.ofNullable(queryVO.getKeyword())
                .filter(StrUtil::isNotBlank)
                .ifPresent(keyword -> wrapper.and(w -> w.like(ApiOrder::getMobileNo, keyword)
                        .or().like(ApiOrder::getOrderId, keyword)
                        .or().like(ApiOrder::getOutOrderId, keyword)));
        
        // 使用流处理排序
        applySorting(wrapper, queryVO);
        
        return wrapper;
    }
    
    /**
     * 构建字符串条件
     */
    private Runnable buildCondition(String value, Consumer<String> action) {
        return () -> Optional.ofNullable(value)
                .filter(StrUtil::isNotBlank)
                .ifPresent(action);
    }
    
    /**
     * 构建时间条件
     */
    private Runnable buildTimeCondition(LocalDateTime time, Consumer<LocalDateTime> action) {
        return () -> Optional.ofNullable(time)
                .ifPresent(action);
    }
    
    /**
     * 时间转换
     */
    private java.util.Date convertToDate(LocalDateTime time) {
        return java.util.Date.from(time.atZone(ZoneId.systemDefault()).toInstant());
    }
    
    /**
     * 使用流处理排序逻辑
     */
    private void applySorting(LambdaQueryWrapper<ApiOrder> wrapper, ApiOrderQueryVO queryVO) {
        // 排序字段映射
        Map<String, Consumer<Boolean>> sortMap = Map.of(
                "productNo", isAsc -> wrapper.orderBy(true, isAsc, ApiOrder::getProductNo),
                "mobileNo", isAsc -> wrapper.orderBy(true, isAsc, ApiOrder::getMobileNo),
                "orderStatus", isAsc -> wrapper.orderBy(true, isAsc, ApiOrder::getOrderStatus),
                "outOrderStatus", isAsc -> wrapper.orderBy(true, isAsc, ApiOrder::getOutOrderStatus),
                "operationType", isAsc -> wrapper.orderBy(true, isAsc, ApiOrder::getOperationType)
        );
        
        Optional.ofNullable(queryVO.getSortField())
                .filter(StrUtil::isNotBlank)
                .map(sortField -> {
                    boolean isAsc = "asc".equalsIgnoreCase(queryVO.getSortOrder());
                    return sortMap.getOrDefault(sortField, isAsc2 -> wrapper.orderByDesc(ApiOrder::getCreateTime));
                })
                .ifPresentOrElse(
                        consumer -> consumer.accept("asc".equalsIgnoreCase(queryVO.getSortOrder())),
                        () -> wrapper.orderByDesc(ApiOrder::getCreateTime)
                );
    }
    
    /**
     * 使用流转换分页结果
     */
    private IPage<ApiOrderVO> convertToVOPage(IPage<ApiOrder> pageResult) {
        List<ApiOrderVO> voList = pageResult.getRecords()
                .stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
        
        Page<ApiOrderVO> voPage = new Page<>(pageResult.getCurrent(), pageResult.getSize(), pageResult.getTotal());
        voPage.setRecords(voList);
        return voPage;
    }
    
    /**
     * 实体转VO
     */
    private ApiOrderVO convertToVO(ApiOrder entity) {
        return Optional.ofNullable(entity)
                .map(e -> {
                    ApiOrderVO vo = new ApiOrderVO();
                    BeanUtils.copyProperties(e, vo);
                    return vo;
                })
                .orElse(new ApiOrderVO());
    }
}