<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xy.admin.mapper.promote.ApiOrderMapper">
  <resultMap id="BaseResultMap" type="com.xy.admin.entity.ApiOrder">
    <!--@mbg.generated-->
    <!--@Table api_order-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="channel_no" jdbcType="VARCHAR" property="channelNo" />
    <result column="product_no" jdbcType="VARCHAR" property="productNo" />
    <result column="mobile_no" jdbcType="VARCHAR" property="mobileNo" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="order_status" jdbcType="VARCHAR" property="orderStatus" />
    <result column="out_order_status" jdbcType="VARCHAR" property="outOrderStatus" />
    <result column="out_order_id" jdbcType="VARCHAR" property="outOrderId" />
    <result column="operation_type" jdbcType="VARCHAR" property="operationType" />
    <result column="client_ip" jdbcType="VARCHAR" property="clientIp" />
    <result column="user_agent" jdbcType="VARCHAR" property="userAgent" />
    <result column="app_package" jdbcType="VARCHAR" property="appPackage" />
    <result column="app_name" jdbcType="VARCHAR" property="appName" />
    <result column="platform" jdbcType="VARCHAR" property="platform" />
    <result column="page_url" jdbcType="VARCHAR" property="pageUrl" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="server" jdbcType="VARCHAR" property="server" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, app_id, channel_no, product_no, mobile_no, order_id, order_status, out_order_status, 
    out_order_id, operation_type, client_ip, user_agent, app_package, app_name, platform, 
    page_url, remark, create_time, update_time, server
  </sql>
  <update id="updateByPrimaryKey" parameterType="com.xy.admin.entity.ApiOrder">
    <!--@mbg.generated-->
    update api_order
    set app_id = #{appId,jdbcType=VARCHAR},
      channel_no = #{channelNo,jdbcType=VARCHAR},
      product_no = #{productNo,jdbcType=VARCHAR},
      mobile_no = #{mobileNo,jdbcType=VARCHAR},
      order_id = #{orderId,jdbcType=VARCHAR},
      order_status = #{orderStatus,jdbcType=VARCHAR},
      out_order_status = #{outOrderStatus,jdbcType=VARCHAR},
      out_order_id = #{outOrderId,jdbcType=VARCHAR},
      operation_type = #{operationType,jdbcType=VARCHAR},
      client_ip = #{clientIp,jdbcType=VARCHAR},
      user_agent = #{userAgent,jdbcType=VARCHAR},
      app_package = #{appPackage,jdbcType=VARCHAR},
      app_name = #{appName,jdbcType=VARCHAR},
      platform = #{platform,jdbcType=VARCHAR},
      page_url = #{pageUrl,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      server = #{server,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!-- 动态分组查询 -->
  <select id="selectGroupData" resultType="java.util.Map">
    SELECT
    <foreach collection="query.groupFields" item="field" separator=",">
      <choose>
        <when test="field == 'DATE(create_time)'">
          DATE(create_time) as create_date
        </when>
        <when test="field == 'DATE(update_time)'">
          DATE(update_time) as update_date
        </when>
        <otherwise>
          ${field}
        </otherwise>
      </choose>
    </foreach>
    ,
    COUNT(*) as total_count,
    COUNT(DISTINCT mobile_no) as unique_mobile_count,
    COUNT(DISTINCT order_id) as unique_order_count
    <if test="query.aggregateFields != null and query.aggregateFields.size() > 0">
      <foreach collection="query.aggregateFields" item="aggField">
        <choose>
          <when test="aggField == 'COUNT_DISTINCT_mobile_no'">
            , COUNT(DISTINCT mobile_no) as count_distinct_mobile_no
          </when>
          <when test="aggField == 'COUNT_DISTINCT_order_id'">
            , COUNT(DISTINCT order_id) as count_distinct_order_id
          </when>
          <when test="aggField == 'COUNT'">
            , COUNT(*) as count_total
          </when>
        </choose>
      </foreach>
    </if>
    FROM api_order
    <where>
      <if test="query.appId != null and query.appId != ''">
        AND app_id LIKE CONCAT('%', #{query.appId}, '%')
      </if>
      <if test="query.channelNo != null and query.channelNo != ''">
        AND channel_no LIKE CONCAT('%', #{query.channelNo}, '%')
      </if>
      <if test="query.productNo != null and query.productNo != ''">
        AND product_no LIKE CONCAT('%', #{query.productNo}, '%')
      </if>
      <if test="query.mobileNo != null and query.mobileNo != ''">
        AND mobile_no LIKE CONCAT('%', #{query.mobileNo}, '%')
      </if>
      <if test="query.orderStatus != null and query.orderStatus != ''">
        AND order_status = #{query.orderStatus}
      </if>
      <if test="query.outOrderStatus != null and query.outOrderStatus != ''">
        AND out_order_status = #{query.outOrderStatus}
      </if>
      <if test="query.operationType != null and query.operationType != ''">
        AND operation_type = #{query.operationType}
      </if>
      <if test="query.platform != null and query.platform != ''">
        AND platform LIKE CONCAT('%', #{query.platform}, '%')
      </if>
      <if test="query.server != null and query.server != ''">
        AND server LIKE CONCAT('%', #{query.server}, '%')
      </if>
      <if test="query.createTimeStart != null">
        AND create_time >= #{query.createTimeStart}
      </if>
      <if test="query.createTimeEnd != null">
        AND create_time &lt;= #{query.createTimeEnd}
      </if>
      <if test="query.updateTimeStart != null">
        AND update_time >= #{query.updateTimeStart}
      </if>
      <if test="query.updateTimeEnd != null">
        AND update_time &lt;= #{query.updateTimeEnd}
      </if>
    </where>
    GROUP BY
    <foreach collection="query.groupFields" item="field" separator=",">
      <choose>
        <when test="field == 'DATE(create_time)'">
          DATE(create_time)
        </when>
        <when test="field == 'DATE(update_time)'">
          DATE(update_time)
        </when>
        <otherwise>
          ${field}
        </otherwise>
      </choose>
    </foreach>
    <if test="query.orderBy != null and query.orderBy != ''">
      ORDER BY ${query.orderBy}
      <if test="query.orderDirection != null and query.orderDirection != ''">
        ${query.orderDirection}
      </if>
    </if>
    <if test="query.orderBy == null or query.orderBy == ''">
      ORDER BY total_count DESC
    </if>
    <if test="query.limit != null and query.limit > 0">
      LIMIT #{query.limit}
    </if>
  </select>
</mapper>